"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { useForm, SubmitHandler, FormProvider, useFormState } from "react-hook-form"
import type { Resolver, UseFormReturn } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { CalendarIcon, ImageIcon, MapPinIcon, X } from "lucide-react"
import { format, parseISO } from "date-fns"

import { But<PERSON> } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Card, CardContent } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { useEvents, type EventType } from "@/contexts/event-context"
import { supabase } from "@/lib/supabase"
import { CustomFieldsConfig } from "@/components/custom-fields-config"
import { WysiwygEditor } from "@/components/wysiwyg-editor"
import { TicketConfig } from "@/components/ticket-config"

// Define status type
export const statuses = ["draft", "published", "cancelled", "completed"] as const
export type Status = typeof statuses[number]

// Ticket Type Schema
const ticketTypeSchema = z.object({
  id: z.string(),
  name: z.string().min(1, "Ticket name is required"),
  description: z.string().min(1, "Ticket description is required"),
  price: z.number().min(0, "Price must be 0 or greater"),
  maxQuantity: z.number().min(1, "Max quantity must be at least 1"),
  availableQuantity: z.number().min(0, "Available quantity must be 0 or greater"),
  features: z.array(z.string()),
  isPopular: z.boolean().optional(),
  saleStartDate: z.string().optional(),
  saleEndDate: z.string().optional(),
})

// Event Ticket Schema
const eventTicketSchema = z.object({
  ticketType: ticketTypeSchema,
  quantity: z.number().min(0, "Quantity must be 0 or greater"),
})

// Form schema
export const formSchema = z.object({
  title: z.string().min(5, { message: "Title must be at least 5 characters" }),
  description: z.string().min(20, { message: "Description must be at least 20 characters" }),
  description_html: z.string().optional(),
  start_date: z.date(),
  end_date: z.date(),
  location: z.string().min(3, { message: "Location is required" }),
  price: z.string().optional(),
  max_participants: z.string().optional(),
  category_id: z.string().optional(),
  organization_id: z.string().min(1, { message: "Organization is required" }),
  status: z.enum(statuses).default("draft"),
  enable_certificates: z.boolean().default(false),
  enable_attendance: z.boolean().default(true),
  is_public: z.boolean().default(true),
  is_featured: z.boolean().default(false),
  registration_deadline: z.date().optional(),
  images: z.array(z.any()).optional(),
  tickets: z.array(eventTicketSchema).optional(),
  custom_fields: z.array(z.object({
    id: z.string(),
    label: z.string().min(1, "Field label is required"),
    type: z.enum(['text', 'email', 'phone', 'number', 'select', 'checkbox', 'textarea']),
    required: z.boolean(),
    placeholder: z.string().optional(),
    options: z.array(z.string()).optional(),
    validation: z.object({
      minLength: z.number().optional(),
      maxLength: z.number().optional(),
      pattern: z.string().optional(),
    }).optional(),
    order: z.number(),
  })).optional(),
})

// Form values type
type FormValues = z.infer<typeof formSchema>

// Remove the explicit type since we're using z.infer<typeof formSchema> directly

interface EventFormProps {
  event?: EventType
  userId: string
  onSuccess?: (event: EventType) => void
}

export function EventForm({ event, userId, onSuccess }: EventFormProps) {
  const { createEvent, updateEvent } = useEvents()
  const [imageFiles, setImageFiles] = useState<File[]>([])
  const [imagePreviews, setImagePreviews] = useState<string[]>([])
  const [categories, setCategories] = useState<Array<{id: string, name: string, color: string}>>([])
  const [userOrganization, setUserOrganization] = useState<{id: string, name: string} | null>(null)
  const { toast } = useToast()

  // Fetch categories and user's organization on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch categories
        const { data: categoriesData, error: categoriesError } = await supabase
          .from("event_categories")
          .select("id, name, color")
          .eq("is_active", true)
          .order("name")

        if (categoriesError) {
          console.error("Error fetching categories:", categoriesError)
        } else {
          setCategories(categoriesData || [])
        }

        // Fetch user's organization
        const response = await fetch('/api/organizations/user')
        if (response.ok) {
          const data = await response.json()
          if (data.organization) {
            setUserOrganization(data.organization)
          }
        } else {
          console.error("Error fetching user organization")
        }
      } catch (error) {
        console.error("Error fetching data:", error)
      }
    }

    fetchData()
  }, [])

  // Initialize image previews from existing event images
  useEffect(() => {
    if (event?.images && Array.isArray(event.images)) {
      const urls = event.images.map((img: any) => img.url || img)
      setImagePreviews(urls)
    } else if (event?.image_url) {
      setImagePreviews([event.image_url])
    }
  }, [event])

  // Set user's organization as default when loaded (only for new events)
  useEffect(() => {
    if (userOrganization && !event) {
      formMethods.setValue("organization_id", userOrganization.id)
    }
  }, [userOrganization, event, formMethods])

  // Initialize form with default values or existing event data
  const formMethods = useForm<FormValues>({
    // @ts-ignore - The resolver type is not perfectly aligned with the form values type
    resolver: zodResolver(formSchema) as any,
    defaultValues: {
      title: event?.title || "",
      // For WYSIWYG editor, use HTML content if available, otherwise use plain text
      description: event?.description_html || event?.description || "",
      description_html: event?.description_html || "",
      start_date: event?.start_date ? new Date(event.start_date) : new Date(),
      end_date: event?.end_date ? new Date(event.end_date) : new Date(),
      location: event?.location || "",
      price: event?.price?.toString() || "",
      max_participants: event?.max_participants?.toString() || "",
      category_id: event?.category_id || "",
      organization_id: event?.organization_id || "",
      status: event?.status as "draft" | "published" | "cancelled" | "completed" || "draft",
      enable_certificates: event?.enable_certificates || false,
      enable_attendance: event?.enable_attendance ?? true,
      is_public: event?.is_public ?? true,
      is_featured: event?.is_featured || false,
      registration_deadline: event?.registration_deadline ? new Date(event.registration_deadline) : undefined,
      images: [],
      tickets: event?.tickets || [],
      custom_fields: event?.custom_fields || [],
    },
  })

  // Handle multiple image upload
  const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length === 0) return

    // Check if adding these files would exceed the limit of 10 images
    if (imagePreviews.length + files.length > 10) {
      toast({
        title: "Error",
        description: "Maximum 10 images allowed per event",
        variant: "destructive",
      })
      return
    }

    const validFiles: File[] = []

    for (const file of files) {
      // Check file size (max 10MB, will be compressed)
      if (file.size > 10 * 1024 * 1024) {
        toast({
          title: "Error",
          description: `${file.name} is too large. Maximum size is 10MB`,
          variant: "destructive",
        })
        continue
      }

      // Check file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Error",
          description: `${file.name} is not an image file`,
          variant: "destructive",
        })
        continue
      }

      validFiles.push(file)
    }

    if (validFiles.length === 0) return

    try {
      // Show compression message
      toast({
        title: "Processing images...",
        description: `Compressing ${validFiles.length} image(s) for optimal storage`,
      })

      // Import compression function dynamically to avoid SSR issues
      const { compressEventImage } = await import("@/lib/image-compression")

      const compressedFiles: File[] = []
      const newPreviews: string[] = []

      // Process each file
      for (const file of validFiles) {
        try {
          // Compress the image
          const compressedFile = await compressEventImage(file)
          compressedFiles.push(compressedFile)

          // Create preview
          const reader = new FileReader()
          const preview = await new Promise<string>((resolve) => {
            reader.onloadend = () => resolve(reader.result as string)
            reader.readAsDataURL(compressedFile)
          })
          newPreviews.push(preview)
        } catch (error) {
          console.error(`Error compressing ${file.name}:`, error)
          // Fall back to original file if compression fails
          compressedFiles.push(file)
          const reader = new FileReader()
          const preview = await new Promise<string>((resolve) => {
            reader.onloadend = () => resolve(reader.result as string)
            reader.readAsDataURL(file)
          })
          newPreviews.push(preview)
        }
      }

      // Update state
      setImageFiles(prev => [...prev, ...compressedFiles])
      setImagePreviews(prev => [...prev, ...newPreviews])

      // Update form value
      const currentImages = formMethods.getValues("images") || []
      formMethods.setValue("images", [...currentImages, ...compressedFiles])

      toast({
        title: "Images processed",
        description: `${validFiles.length} image(s) added successfully`,
      })
    } catch (error) {
      console.error("Error processing images:", error)
      toast({
        title: "Error",
        description: "Failed to process images",
        variant: "destructive",
      })
    }
  }

  // Remove image by index
  const removeImage = (index: number) => {
    const newPreviews = imagePreviews.filter((_, i) => i !== index)
    const newFiles = imageFiles.filter((_, i) => i !== index)

    setImagePreviews(newPreviews)
    setImageFiles(newFiles)
    formMethods.setValue("images", newFiles)
  }

  const { control, handleSubmit, formState: { errors, isSubmitting }, setValue, watch } = formMethods as unknown as {
    control: any;
    handleSubmit: any;
    formState: { errors: any; isSubmitting: boolean };
    setValue: any;
    watch: (name: string) => any;
  };

  // Handle form submission
  const onSubmit: SubmitHandler<FormValues> = async (data) => {
    try {
      // Prepare event data (slug will be auto-generated by database trigger)
      const eventData: any = {
        title: data.title,
        description: data.description,
        description_html: data.description_html || null,
        start_date: data.start_date.toISOString(),
        end_date: data.end_date.toISOString(),
        location: data.location,
        price: data.price ? Number.parseFloat(data.price) : null,
        max_participants: data.max_participants ? Number.parseInt(data.max_participants, 10) : null,
        category_id: data.category_id || null,
        organization_id: data.organization_id,
        status: data.status,
        created_by: userId,
        enable_certificates: data.enable_certificates,
        enable_attendance: data.enable_attendance,
        is_public: data.is_public,
        is_featured: data.is_featured,
        registration_deadline: data.registration_deadline?.toISOString() || null,
        tickets: data.tickets || [],
        custom_fields: data.custom_fields || [],
        // Keep existing images for now - in a real app, you would upload to storage
        images: event?.images || [],
        image_url: event?.image_url || null, // Keep for backward compatibility
      }

      // Create or update event
      let result
      if (event) {
        result = await updateEvent(event.id, eventData)
      } else {
        result = await createEvent(eventData as Partial<EventType>)
      }

      if (result && onSuccess) {
        onSuccess(result)
      }
    } catch (error) {
      console.error("Error submitting form:", error)
      toast({
        title: "Error",
        description: "Failed to save event",
        variant: "destructive",
      })
    } finally {
      // Form submission handled by react-hook-form
    }
  }

  // Show error if user doesn't have an organization
  if (!userOrganization && !event) {
    return (
      <div className="p-6 text-center">
        <div className="text-red-600 mb-4">
          <h3 className="text-lg font-semibold">Organization Required</h3>
          <p className="text-sm text-muted-foreground">
            You need to be associated with an organization to create events.
            Please contact an administrator to assign you to an organization.
          </p>
        </div>
      </div>
    )
  }

  return (
    <FormProvider {...formMethods}>
      <form onSubmit={formMethods.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid gap-6 w-full">
          <div className="space-y-6 w-full">
            <FormField
              control={control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Event Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter event title" {...field} />
                  </FormControl>
                  <FormDescription>A clear, descriptive title for your event</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <WysiwygEditor
                      value={formMethods.watch("description_html") || field.value}
                      onChange={(text, html) => {
                        field.onChange(text)
                        // Also update the description_html field
                        formMethods.setValue("description_html", html)
                      }}
                      placeholder="Describe your event in detail..."
                      className="min-h-32"
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a detailed description of your event. You can use rich text formatting.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />



            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
              <FormField
                control={control}
                name="start_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Start Date & Time</FormLabel>
                    <div className="flex gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button variant="outline" className="w-full pl-3 text-left font-normal">
                              {field.value ? format(field.value, "PPP") : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={(date) => {
                              if (date) {
                                // Keep the existing time when changing the date
                                const newDate = new Date(date);
                                if (field.value) {
                                  newDate.setHours(
                                    field.value.getHours(),
                                    field.value.getMinutes()
                                  );
                                }
                                field.onChange(newDate);
                              }
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <Input
                        type="time"
                        className="w-28"
                        value={field.value ? format(field.value, 'HH:mm') : ''}
                        onChange={(e) => {
                          if (field.value) {
                            const [hours, minutes] = e.target.value.split(':').map(Number);
                            const newDate = new Date(field.value);
                            newDate.setHours(hours, minutes);
                            field.onChange(newDate);
                          }
                        }}
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="end_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Date & Time</FormLabel>
                    <div className="flex gap-2">
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button variant="outline" className="w-full pl-3 text-left font-normal">
                              {field.value ? format(field.value, "PPP") : <span>Pick a date</span>}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={(date) => {
                              if (date) {
                                // Keep the existing time when changing the date
                                const newDate = new Date(date);
                                if (field.value) {
                                  newDate.setHours(
                                    field.value.getHours(),
                                    field.value.getMinutes()
                                  );
                                }
                                field.onChange(newDate);
                              }
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <Input
                        type="time"
                        className="w-28"
                        value={field.value ? format(field.value, 'HH:mm') : ''}
                        onChange={(e) => {
                          if (field.value) {
                            const [hours, minutes] = e.target.value.split(':').map(Number);
                            const newDate = new Date(field.value);
                            newDate.setHours(hours, minutes);
                            field.onChange(newDate);
                          }
                        }}
                      />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input placeholder="Event location" {...field} />
                      <MapPinIcon className="absolute right-3 top-2.5 h-5 w-5 text-muted-foreground" />
                    </div>
                  </FormControl>
                  <FormDescription>Physical address or online platform</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />



            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
              <FormField
                control={control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price (MYR)</FormLabel>
                    <FormControl>
                      <Input type="number" min="0" step="0.01" placeholder="0.00" {...field} />
                    </FormControl>
                    <FormDescription>Leave empty for free events</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="max_participants"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Max Participants</FormLabel>
                    <FormControl>
                      <Input type="number" min="1" placeholder="Unlimited" {...field} />
                    </FormControl>
                    <FormDescription>Maximum number of attendees</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Ticket Configuration */}
            <TicketConfig name="tickets" />

            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2">
              <FormField
                control={control}
                name="category_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value || undefined}>
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: category.color }}
                              />
                              {category.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormDescription>Categorize your event for better discoverability</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="organization_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Organization *</FormLabel>
                    <FormControl>
                      <div className="flex items-center gap-2">
                        <Input
                          value={userOrganization?.name || "Loading..."}
                          disabled
                          className="bg-muted"
                        />
                        {userOrganization && (
                          <div className="text-sm text-muted-foreground">
                            (Auto-assigned)
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormDescription>Your organization is automatically assigned to this event</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={control}
              name="images"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Event Images (Max 10)</FormLabel>
                  <FormControl>
                    <div className="space-y-4">
                      {/* Image Grid */}
                      {imagePreviews.length > 0 && (
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                          {imagePreviews.map((preview, index) => (
                            <div key={index} className="relative aspect-video">
                              <img
                                src={preview}
                                alt={`Event image ${index + 1}`}
                                className="h-full w-full object-cover rounded-md border"
                              />
                              <Button
                                type="button"
                                variant="destructive"
                                size="sm"
                                className="absolute -top-2 -right-2 h-6 w-6 rounded-full p-0"
                                onClick={() => removeImage(index)}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                              {index === 0 && (
                                <div className="absolute bottom-1 left-1 bg-blue-600 text-white text-xs px-1 rounded">
                                  Primary
                                </div>
                              )}
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Upload Area */}
                      {imagePreviews.length < 10 && (
                        <label className="flex aspect-video w-full cursor-pointer flex-col items-center justify-center gap-2 border-2 border-dashed border-muted-foreground/25 p-4 text-muted-foreground hover:bg-muted/50 rounded-md">
                          <ImageIcon className="h-8 w-8" />
                          <span>Upload event images</span>
                          <span className="text-xs">Max 10 images, 10MB each</span>
                          <span className="text-xs">({imagePreviews.length}/10 uploaded)</span>
                          <Input
                            type="file"
                            accept="image/*"
                            multiple
                            className="hidden"
                            onChange={handleImageChange}
                          />
                        </label>
                      )}
                    </div>
                  </FormControl>
                  <FormDescription>
                    Add up to 10 images for your event. The first image will be used as the primary image.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={control}
              name="registration_deadline"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Registration Deadline (Optional)</FormLabel>
                  <div className="flex gap-2">
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button variant="outline" className="w-full pl-3 text-left font-normal">
                            {field.value ? format(field.value, "PPP") : <span>No deadline</span>}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={(date) => {
                            if (date) {
                              // Keep the existing time when changing the date
                              const newDate = new Date(date);
                              if (field.value) {
                                newDate.setHours(
                                  field.value.getHours(),
                                  field.value.getMinutes()
                                );
                              }
                              field.onChange(newDate);
                            } else {
                              field.onChange(null);
                            }
                          }}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <Input
                      type="time"
                      className="w-28"
                      value={field.value ? format(field.value, 'HH:mm') : ''}
                      onChange={(e) => {
                        if (field.value) {
                          const [hours, minutes] = e.target.value.split(':').map(Number);
                          const newDate = new Date(field.value);
                          newDate.setHours(hours, minutes);
                          field.onChange(newDate);
                        }
                      }}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => field.onChange(null)}
                    >
                      Clear
                    </Button>
                  </div>
                  <FormDescription>When registration closes for this event</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-4 rounded-md border p-4 w-full">
              <FormField
                control={control}
                name="status"
                render={({ field }) => (
                  <FormItem className="space-y-3">
                    <FormLabel>Event Status</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={(value: "draft" | "published" | "cancelled" | "completed") => field.onChange(value)}
                      >
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Select status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="published">Published</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="is_public"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="data-[state=checked]:bg-green-600 data-[state=unchecked]:bg-gray-200"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Public Event</FormLabel>
                      <FormDescription>Make this event visible to everyone</FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="enable_attendance"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Enable Attendance</FormLabel>
                      <FormDescription>Allow tracking of event attendance</FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="enable_certificates"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={!watch('enable_attendance')}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Issue Certificates</FormLabel>
                      <FormDescription>Enable to automatically generate certificates for attendees</FormDescription>
                    </div>
                  </FormItem>
                )}
              />

              <FormField
                control={control}
                name="is_featured"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                    <FormControl>
                      <Checkbox
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel>Featured Event</FormLabel>
                      <FormDescription>Mark this event as featured for better visibility</FormDescription>
                    </div>
                  </FormItem>
                )}
              />
            </div>

            {/* Custom Fields Configuration */}
            <CustomFieldsConfig name="custom_fields" />
          </div>
        </div>

        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline">
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Saving..." : event ? "Update Event" : "Create Event"}
          </Button>
        </div>
      </form>
    </FormProvider>
  );
};
