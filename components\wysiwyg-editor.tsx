"use client"

import { useEffect, useRef, forwardRef, useImperativeHandle, useState, useCallback } from "react"
import { cn } from "@/lib/utils"
import dynamic from "next/dynamic"
import { marked } from "marked"

// Dynamically import MDEditor to avoid SSR issues
const MDEditor = dynamic(
  () => import("@uiw/react-md-editor").then((mod) => mod.default),
  {
    ssr: false,
    loading: () => <EditorLoading />
  }
)

// Simple loading component
const EditorLoading = () => (
  <div className="border rounded-md min-h-[120px] bg-gray-50 animate-pulse flex items-center justify-center">
    <span className="text-gray-500">Loading editor...</span>
  </div>
)

interface WysiwygEditorProps {
  value?: string
  onChange?: (value: string, html: string) => void
  placeholder?: string
  className?: string
  readOnly?: boolean
  theme?: "light" | "dark"
}

export interface WysiwygEditorRef {
  focus: () => void
  blur: () => void
  getEditor: () => any
}

const WysiwygEditor = forwardRef<WysiwygEditorRef, WysiwygEditorProps>(
  ({ value = "", onChange, placeholder, className, readOnly = false, theme = "light" }, ref) => {
    const editorRef = useRef<any>(null)
    const [isMounted, setIsMounted] = useState(false)
    const [markdownValue, setMarkdownValue] = useState(value)

    useEffect(() => {
      setIsMounted(true)
    }, [])

    useEffect(() => {
      setMarkdownValue(value)
    }, [value])

    useImperativeHandle(ref, () => ({
      focus: () => {
        try {
          if (editorRef.current) {
            const textarea = editorRef.current.querySelector('textarea')
            if (textarea) {
              textarea.focus()
            }
          }
        } catch (error) {
          console.warn('Error focusing WYSIWYG editor:', error)
        }
      },
      blur: () => {
        try {
          if (editorRef.current) {
            const textarea = editorRef.current.querySelector('textarea')
            if (textarea) {
              textarea.blur()
            }
          }
        } catch (error) {
          console.warn('Error blurring WYSIWYG editor:', error)
        }
      },
      getEditor: () => {
        try {
          return editorRef.current || null
        } catch (error) {
          console.warn('Error getting WYSIWYG editor instance:', error)
          return null
        }
      },
    }), [])

    const handleChange = useCallback(async (val?: string) => {
      try {
        const newValue = val || ""
        setMarkdownValue(newValue)

        if (onChange) {
          // Convert markdown to HTML for compatibility
          const htmlValue = await marked(newValue, {
            breaks: true,
            gfm: true
          })
          onChange(newValue, htmlValue)
        }
      } catch (error) {
        console.warn('Error in WYSIWYG editor change handler:', error)
        if (onChange) {
          onChange("", "")
        }
      }
    }, [onChange])

    if (!isMounted) {
      return (
        <div className={cn("wysiwyg-editor", className)}>
          <EditorLoading />
        </div>
      )
    }

    return (
      <div className={cn("wysiwyg-editor", className)} ref={editorRef}>
        <MDEditor
          value={markdownValue}
          onChange={handleChange}
          data-color-mode={theme}
          preview="edit"
          hideToolbar={readOnly}
          visibleDragBar={false}
          textareaProps={{
            placeholder: placeholder || "Enter your content...",
            style: {
              fontSize: 14,
              lineHeight: 1.5,
              minHeight: 120,
            },
            readOnly,
          }}
          height={200}
        />
        <style jsx global>{`
          .wysiwyg-editor .w-md-editor {
            background-color: transparent;
          }

          .wysiwyg-editor .w-md-editor-text-container,
          .wysiwyg-editor .w-md-editor-text,
          .wysiwyg-editor .w-md-editor-text-input,
          .wysiwyg-editor .w-md-editor-text-textarea {
            font-size: 14px !important;
            line-height: 1.5 !important;
            font-family: inherit !important;
          }

          .wysiwyg-editor .w-md-editor-text-textarea {
            min-height: 120px !important;
            resize: vertical;
          }

          .wysiwyg-editor .w-md-editor.w-md-editor-focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 1px #3b82f6;
          }

          /* Dark mode support */
          .wysiwyg-editor .w-md-editor[data-color-mode="dark"] {
            background-color: #1f2937;
            border-color: #374151;
          }

          .wysiwyg-editor .w-md-editor[data-color-mode="dark"] .w-md-editor-text-textarea {
            background-color: #1f2937;
            color: #f9fafb;
            border-color: #374151;
          }

          .wysiwyg-editor .w-md-editor[data-color-mode="dark"] .w-md-editor-text-textarea::placeholder {
            color: #6b7280;
          }

          /* Light mode */
          .wysiwyg-editor .w-md-editor[data-color-mode="light"] {
            background-color: white;
            border-color: #e2e8f0;
          }

          .wysiwyg-editor .w-md-editor[data-color-mode="light"] .w-md-editor-text-textarea {
            background-color: white;
            color: #1f2937;
            border-color: #e2e8f0;
          }

          .wysiwyg-editor .w-md-editor[data-color-mode="light"] .w-md-editor-text-textarea::placeholder {
            color: #9ca3af;
          }

          /* Hide toolbar when readonly */
          .wysiwyg-editor .w-md-editor .w-md-editor-toolbar {
            display: ${readOnly ? 'none' : 'flex'};
          }

          /* Ensure proper border radius */
          .wysiwyg-editor .w-md-editor {
            border-radius: 6px;
            overflow: hidden;
          }
        `}</style>
      </div>
    )
  }
)

WysiwygEditor.displayName = "WysiwygEditor"

export { WysiwygEditor }
