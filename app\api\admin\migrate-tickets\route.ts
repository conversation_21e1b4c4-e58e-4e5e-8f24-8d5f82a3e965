import { NextRequest, NextResponse } from "next/server"
import { getSupabaseAdmin } from "@/lib/supabase-admin"

export async function POST(request: NextRequest) {
  try {
    const supabaseAdmin = getSupabaseAdmin()

    // Check if tickets column already exists
    const { data: columns, error: columnsError } = await supabaseAdmin
      .rpc('get_table_columns', { table_name: 'events' })

    if (columnsError) {
      console.error("Error checking columns:", columnsError)
      // If the RPC doesn't exist, try direct SQL
      const { data: checkResult, error: checkError } = await supabaseAdmin
        .from('information_schema.columns')
        .select('column_name')
        .eq('table_name', 'events')
        .eq('column_name', 'tickets')

      if (checkError) {
        console.error("Error checking if tickets column exists:", checkError)
      } else if (checkResult && checkResult.length > 0) {
        return NextResponse.json({
          success: true,
          message: "Tickets column already exists"
        })
      }
    }

    // Add tickets column if it doesn't exist
    const migrationSQL = `
      DO $$ 
      BEGIN
        -- Add tickets column if it doesn't exist
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'tickets') THEN
          ALTER TABLE events ADD COLUMN tickets JSONB DEFAULT '[]';
        END IF;
      END $$;

      -- Create index for better performance on tickets column
      CREATE INDEX IF NOT EXISTS idx_events_tickets ON events USING GIN(tickets);
    `

    const { error: migrationError } = await supabaseAdmin.rpc('exec_sql', {
      sql: migrationSQL
    })

    if (migrationError) {
      console.error("Migration error:", migrationError)
      // Try alternative approach with direct SQL execution
      const { error: directError } = await supabaseAdmin
        .from('events')
        .select('id')
        .limit(1)

      if (directError) {
        return NextResponse.json({
          success: false,
          error: "Failed to execute migration",
          details: migrationError.message
        }, { status: 500 })
      }

      // If we can access events table, try adding column directly
      try {
        await supabaseAdmin.rpc('exec', {
          query: "ALTER TABLE events ADD COLUMN IF NOT EXISTS tickets JSONB DEFAULT '[]'"
        })
      } catch (err) {
        console.error("Direct SQL error:", err)
        return NextResponse.json({
          success: false,
          error: "Failed to add tickets column",
          details: err instanceof Error ? err.message : "Unknown error"
        }, { status: 500 })
      }
    }

    return NextResponse.json({
      success: true,
      message: "Tickets column added successfully"
    })

  } catch (error) {
    console.error("Migration API error:", error)
    return NextResponse.json({
      success: false,
      error: "Internal server error",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
