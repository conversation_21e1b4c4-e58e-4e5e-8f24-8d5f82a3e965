{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "patch-package", "check-crypto": "node scripts/check-crypto.js", "check-env": "node scripts/check-env.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/utilities": "^3.2.2", "@hookform/resolvers": "latest", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "latest", "@types/jsonwebtoken": "^9.0.9", "@types/jspdf": "^2.0.0", "@types/qrcode": "^1.5.5", "@uiw/react-md-editor": "^4.0.7", "browser-image-compression": "^2.0.2", "chart.js": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "crypto-browserify": "^3.12.1", "date-fns": "2.30.0", "embla-carousel-react": "8.5.1", "html2canvas": "^1.4.1", "html5-qrcode": "latest", "input-otp": "1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "lucide-react": "^0.454.0", "marked": "^15.0.12", "next": "15.2.4", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "qrcode": "^1.5.4", "qrcode.react": "latest", "quill": "^2.0.3", "react": "^18.3.1", "react-chartjs-2": "latest", "react-day-picker": "8.10.1", "react-dom": "^18.3.1", "react-dropzone": "latest", "react-hook-form": "latest", "react-resizable-panels": "^2.1.9", "recharts": "2.15.0", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uuid": "latest", "vaul": "^0.9.9", "zod": "latest"}, "devDependencies": {"@types/node": "^22.15.21", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "depcheck": "^1.4.7", "dotenv": "^16.5.0", "patch-package": "6.4.7", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}}