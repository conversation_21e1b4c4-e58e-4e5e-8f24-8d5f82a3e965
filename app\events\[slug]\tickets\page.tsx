"use client"

import { useState, useEffect } from "react"
import * as React from "react"
import { useRouter } from "next/navigation"
import { ArrowLeft, ArrowRight, Minus, Plus, ShoppingCart, Calendar, MapPin, Clock, Users, Star, Check, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { useEvents, type EventType } from "@/contexts/event-context"
import MainNav from "@/components/main-nav"
import { useToast } from "@/hooks/use-toast"

// Define ticket types
export interface TicketType {
  id: string
  name: string
  description: string
  price: number
  maxQuantity: number
  availableQuantity: number
  features: string[]
  isPopular?: boolean
}

export interface SelectedTicket {
  ticketType: TicketType
  quantity: number
}

interface TicketSelectionPageProps {
  params: Promise<{
    slug: string
  }>
}

export default function TicketSelectionPage({ params }: TicketSelectionPageProps) {
  const router = useRouter()
  const { toast } = useToast()
  const { getEventBySlug } = useEvents()
  const [event, setEvent] = useState<EventType | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedTickets, setSelectedTickets] = useState<SelectedTicket[]>([])
  const [currentStep, setCurrentStep] = useState<"selection" | "review" | "attendees">("selection")
  const [isProcessing, setIsProcessing] = useState(false)

  // Get the slug from params using React.use()
  const { slug } = React.use(params)

  useEffect(() => {
    if (!slug) {
      setError("No event specified")
      setLoading(false)
      return
    }

    const fetchEvent = async () => {
      setLoading(true)
      try {
        const eventData = await getEventBySlug(slug)
        if (!eventData) {
          setError("Event not found")
          return
        }
        setEvent(eventData)
      } catch (err: any) {
        setError(err.message || "Failed to load event details")
      } finally {
        setLoading(false)
      }
    }

    fetchEvent()
  }, [slug, getEventBySlug])

  // Get ticket types from event data or generate fallback
  const getTicketTypes = (): TicketType[] => {
    if (!event) return []

    // If event has tickets defined in the database, use those
    if (event.tickets && event.tickets.length > 0) {
      return event.tickets.map(eventTicket => eventTicket.ticketType)
    }

    // Fallback: Generate ticket types based on legacy price field
    const basePrice = event.price || 0

    if (basePrice === 0) {
      // Free event - only one ticket type
      return [
        {
          id: "free",
          name: "Free Ticket",
          description: "Complimentary access to the event",
          price: 0,
          maxQuantity: 10,
          availableQuantity: 100,
          features: ["Event access", "Digital materials"]
        }
      ]
    }

    // Paid event - multiple ticket types (fallback for legacy events)
    return [
      {
        id: "early-bird",
        name: "Early Bird",
        description: "Limited time offer with special pricing",
        price: Math.round(basePrice * 0.8), // 20% discount
        maxQuantity: 5,
        availableQuantity: 25,
        features: ["Event access", "Digital materials", "Early bird discount"],
        isPopular: true
      },
      {
        id: "standard",
        name: "Standard",
        description: "Regular admission ticket",
        price: basePrice,
        maxQuantity: 10,
        availableQuantity: 150,
        features: ["Event access", "Digital materials", "Networking session"]
      },
      {
        id: "vip",
        name: "VIP",
        description: "Premium experience with exclusive benefits",
        price: Math.round(basePrice * 1.5), // 50% premium
        maxQuantity: 3,
        availableQuantity: 20,
        features: ["Event access", "Digital materials", "VIP seating", "Meet & greet", "Premium lunch"]
      }
    ]
  }

  const ticketTypes = getTicketTypes()

  const updateTicketQuantity = (ticketTypeId: string, newQuantity: number) => {
    const ticketType = ticketTypes.find(t => t.id === ticketTypeId)
    if (!ticketType) return

    // Ensure quantity is within bounds
    const clampedQuantity = Math.max(0, Math.min(newQuantity, ticketType.maxQuantity))

    setSelectedTickets(prev => {
      const existing = prev.find(t => t.ticketType.id === ticketTypeId)

      if (clampedQuantity === 0) {
        // Remove ticket if quantity is 0
        return prev.filter(t => t.ticketType.id !== ticketTypeId)
      }

      if (existing) {
        // Update existing ticket quantity
        return prev.map(t =>
          t.ticketType.id === ticketTypeId
            ? { ...t, quantity: clampedQuantity }
            : t
        )
      } else {
        // Add new ticket
        return [...prev, { ticketType, quantity: clampedQuantity }]
      }
    })
  }

  const getTicketQuantity = (ticketTypeId: string): number => {
    return selectedTickets.find(t => t.ticketType.id === ticketTypeId)?.quantity || 0
  }

  const calculateTotal = (): number => {
    return selectedTickets.reduce((total, ticket) => {
      return total + (ticket.ticketType.price * ticket.quantity)
    }, 0)
  }

  const getTotalTickets = (): number => {
    return selectedTickets.reduce((total, ticket) => total + ticket.quantity, 0)
  }

  const handleContinue = async () => {
    if (selectedTickets.length === 0) {
      toast({
        title: "No tickets selected",
        description: "Please select at least one ticket to continue",
        variant: "destructive"
      })
      return
    }

    if (currentStep === "selection") {
      setCurrentStep("review")
    } else if (currentStep === "review") {
      setIsProcessing(true)
      try {
        // Small delay to show loading state
        await new Promise(resolve => setTimeout(resolve, 1000))

        // Store selected tickets in session storage and navigate to register
        sessionStorage.setItem('selectedTickets', JSON.stringify(selectedTickets))
        router.push(`/events/${slug}/register`)
      } catch (error) {
        toast({
          title: "Error",
          description: "Something went wrong. Please try again.",
          variant: "destructive"
        })
      } finally {
        setIsProcessing(false)
      }
    }
  }

  const handleBack = () => {
    if (currentStep === "review") {
      setCurrentStep("selection")
    } else {
      router.push(`/events/${slug}`)
    }
  }

  const clearCart = () => {
    setSelectedTickets([])
    setCurrentStep("selection")
  }

  // Show loading state
  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <MainNav />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-center min-h-[50vh]">
              <div className="text-center">
                <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-muted-foreground">Loading event details...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    )
  }

  // Show error state
  if (error || !event) {
    return (
      <div className="flex min-h-screen flex-col">
        <MainNav />
        <main className="flex-1">
          <div className="container mx-auto px-4 py-8">
            <div className="flex flex-col items-center justify-center min-h-[50vh]">
              <div className="text-destructive text-5xl mb-4">⚠️</div>
              <h1 className="text-2xl font-bold mb-4">{error || "Event not found"}</h1>
              <p className="text-muted-foreground mb-6">
                The event you're looking for doesn't exist or has been removed.
              </p>
              <Button onClick={() => router.push("/")}>Return to Home</Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  const stepProgress = currentStep === "selection" ? 33 : currentStep === "review" ? 66 : 100

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-br from-purple-50 via-white to-blue-50">
      <MainNav />
      <main className="flex-1">
        <div className="container mx-auto px-4 py-8 max-w-7xl">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center gap-4 mb-6">
              <Button variant="ghost" size="sm" onClick={handleBack}>
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>
              <div className="flex-1">
                <h1 className="text-3xl font-bold">Select Your Tickets</h1>
                <p className="text-muted-foreground">Choose your ticket type and quantity for {event.title}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm text-muted-foreground">
                <span className={currentStep === "selection" ? "text-primary font-medium" : ""}>
                  1. Select Tickets
                </span>
                <span className={currentStep === "review" ? "text-primary font-medium" : ""}>
                  2. Review Order
                </span>
                <span className={currentStep === "attendees" ? "text-primary font-medium" : ""}>
                  3. Registration
                </span>
              </div>
              <Progress value={stepProgress} className="h-2" />
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Left Column - Event Info & Tickets */}
            <div className="lg:col-span-2 space-y-6">
              {/* Event Summary Card */}
              <Card className="border-2 border-primary/20 bg-gradient-to-r from-primary/5 to-primary/10">
                <CardContent className="p-6">
                  <h3 className="font-semibold text-xl mb-4">{event.title}</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">
                        {new Date(event.start_date).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">
                        {new Date(event.start_date).toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" })}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">{event.location}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-primary" />
                      <span className="text-muted-foreground">
                        {event.max_participants ? `${event.max_participants} max` : "Unlimited"}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {currentStep === "selection" && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold">Available Tickets</h3>
                  <div className="grid gap-6">
                    {ticketTypes.map((ticketType) => (
                      <Card key={ticketType.id} className={`relative transition-all duration-300 hover:shadow-xl ${
                        ticketType.isPopular
                          ? 'ring-2 ring-primary shadow-lg border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10'
                          : 'hover:border-primary/30'
                      } ${getTicketQuantity(ticketType.id) > 0 ? 'bg-primary/5 ring-1 ring-primary/30 shadow-md' : ''}`}>
                        {ticketType.isPopular && (
                          <div className="absolute -top-3 left-6 z-10">
                            <Badge className="bg-gradient-to-r from-primary to-primary/80 text-white px-3 py-1 shadow-lg">
                              <Star className="w-3 h-3 mr-1" />
                              Most Popular
                            </Badge>
                          </div>
                        )}

                        <CardContent className="p-6">
                          <div className="flex flex-col md:flex-row md:items-center justify-between gap-6">
                            {/* Ticket Info */}
                            <div className="flex-1">
                              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 mb-4">
                                <div>
                                  <h4 className="text-xl font-bold">{ticketType.name}</h4>
                                  <p className="text-muted-foreground">{ticketType.description}</p>
                                </div>
                                <div className="text-2xl font-bold text-primary">
                                  {ticketType.price === 0 ? 'Free' : `RM ${ticketType.price.toFixed(2)}`}
                                </div>
                              </div>

                              {/* Features */}
                              <div className="flex flex-wrap gap-2 mb-4">
                                {ticketType.features.map((feature, index) => (
                                  <Badge key={index} variant="secondary" className="text-xs">
                                    <Check className="w-3 h-3 mr-1" />
                                    {feature}
                                  </Badge>
                                ))}
                              </div>

                              {/* Availability Info */}
                              <div className="flex gap-6 text-sm text-muted-foreground mb-4">
                                <span>Available: {ticketType.availableQuantity}</span>
                                <span>Max per order: {ticketType.maxQuantity}</span>
                              </div>
                            </div>

                            {/* Quantity Selector */}
                            <div className="flex flex-col items-center gap-4">
                              <div className="flex items-center gap-3">
                                <span className="text-sm font-medium">Quantity:</span>
                                <div className="flex items-center border-2 rounded-lg bg-background">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-12 w-12 rounded-r-none"
                                    onClick={() => updateTicketQuantity(ticketType.id, getTicketQuantity(ticketType.id) - 1)}
                                    disabled={getTicketQuantity(ticketType.id) === 0}
                                  >
                                    <Minus className="h-4 w-4" />
                                  </Button>
                                  <div className="w-16 h-12 flex items-center justify-center border-x-2 bg-muted/30 font-bold text-lg">
                                    {getTicketQuantity(ticketType.id)}
                                  </div>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-12 w-12 rounded-l-none"
                                    onClick={() => updateTicketQuantity(ticketType.id, getTicketQuantity(ticketType.id) + 1)}
                                    disabled={getTicketQuantity(ticketType.id) >= ticketType.maxQuantity}
                                  >
                                    <Plus className="h-4 w-4" />
                                  </Button>
                                </div>
                              </div>

                              {getTicketQuantity(ticketType.id) > 0 && (
                                <div className="text-center">
                                  <div className="text-xl font-bold text-primary">
                                    RM {(ticketType.price * getTicketQuantity(ticketType.id)).toFixed(2)}
                                  </div>
                                  <div className="text-xs text-muted-foreground">
                                    {getTicketQuantity(ticketType.id)} × RM {ticketType.price.toFixed(2)}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              )}

              {currentStep === "review" && (
                <div className="space-y-6">
                  <h3 className="text-xl font-semibold">Review Your Order</h3>
                  <Card>
                    <CardHeader>
                      <CardTitle>Selected Tickets</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {selectedTickets.map((ticket) => (
                        <div key={ticket.ticketType.id} className="flex items-center justify-between p-4 border rounded-lg">
                          <div className="flex-1">
                            <h4 className="font-semibold">{ticket.ticketType.name}</h4>
                            <p className="text-sm text-muted-foreground">
                              RM {ticket.ticketType.price.toFixed(2)} each × {ticket.quantity}
                            </p>
                            <div className="flex flex-wrap gap-1 mt-2">
                              {ticket.ticketType.features.map((feature, index) => (
                                <Badge key={index} variant="outline" className="text-xs">
                                  {feature}
                                </Badge>
                              ))}
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold">
                              RM {(ticket.ticketType.price * ticket.quantity).toFixed(2)}
                            </div>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setCurrentStep("selection")}
                              className="text-xs text-muted-foreground hover:text-primary"
                            >
                              Edit
                            </Button>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </div>
              )}
            </div>

            {/* Right Column - Shopping Cart */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                <Card className="border-2 border-primary/20">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-2">
                      <ShoppingCart className="h-5 w-5" />
                      Your Cart
                      {selectedTickets.length > 0 && (
                        <Badge variant="secondary" className="ml-auto">
                          {getTotalTickets()} item{getTotalTickets() !== 1 ? 's' : ''}
                        </Badge>
                      )}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {selectedTickets.length === 0 ? (
                      <div className="text-center py-8">
                        <div className="p-4 bg-muted rounded-full w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                          <ShoppingCart className="h-8 w-8 text-muted-foreground" />
                        </div>
                        <h4 className="font-medium text-muted-foreground mb-2">Your cart is empty</h4>
                        <p className="text-sm text-muted-foreground">
                          Select tickets to add them to your cart
                        </p>
                      </div>
                    ) : (
                      <>
                        {/* Cart Items */}
                        <div className="space-y-3">
                          {selectedTickets.map((ticket) => (
                            <div key={ticket.ticketType.id} className="p-3 border rounded-lg bg-background">
                              <div className="flex justify-between items-start mb-2">
                                <div className="flex-1">
                                  <h4 className="font-semibold text-sm">{ticket.ticketType.name}</h4>
                                  <p className="text-xs text-muted-foreground">
                                    RM {ticket.ticketType.price.toFixed(2)} each
                                  </p>
                                </div>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0 text-muted-foreground hover:text-destructive"
                                  onClick={() => updateTicketQuantity(ticket.ticketType.id, 0)}
                                >
                                  <X className="h-3 w-3" />
                                </Button>
                              </div>

                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="h-7 w-7 p-0"
                                    onClick={() => updateTicketQuantity(ticket.ticketType.id, ticket.quantity - 1)}
                                    disabled={ticket.quantity <= 1}
                                  >
                                    <Minus className="h-3 w-3" />
                                  </Button>
                                  <span className="w-6 text-center text-sm font-medium">{ticket.quantity}</span>
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="h-7 w-7 p-0"
                                    onClick={() => updateTicketQuantity(ticket.ticketType.id, ticket.quantity + 1)}
                                    disabled={ticket.quantity >= ticket.ticketType.maxQuantity}
                                  >
                                    <Plus className="h-3 w-3" />
                                  </Button>
                                </div>
                                <div className="text-right">
                                  <div className="font-bold text-sm">
                                    RM {(ticket.ticketType.price * ticket.quantity).toFixed(2)}
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>

                        <Separator />

                        {/* Order Summary */}
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>Subtotal ({getTotalTickets()} ticket{getTotalTickets() !== 1 ? 's' : ''})</span>
                            <span>RM {calculateTotal().toFixed(2)}</span>
                          </div>
                          <div className="flex justify-between text-sm text-muted-foreground">
                            <span>Processing fee</span>
                            <span>RM 0.00</span>
                          </div>
                          <Separator />
                          <div className="flex justify-between text-lg font-bold">
                            <span>Total</span>
                            <span className="text-primary">RM {calculateTotal().toFixed(2)}</span>
                          </div>
                        </div>

                        <Separator />

                        {/* Action Buttons */}
                        <div className="space-y-2">
                          <Button
                            onClick={handleContinue}
                            className="w-full"
                            size="lg"
                            disabled={isProcessing}
                          >
                            {isProcessing ? (
                              <>
                                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                                Processing...
                              </>
                            ) : (
                              <>
                                {currentStep === "selection" ? "Review Order" : "Continue to Registration"}
                                <ArrowRight className="ml-2 h-4 w-4" />
                              </>
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            onClick={clearCart}
                            className="w-full"
                            size="sm"
                          >
                            Clear Cart
                          </Button>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>

          {/* Mobile Cart Summary - Fixed Bottom */}
          {selectedTickets.length > 0 && (
            <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-background border-t-2 border-primary/20 p-4 shadow-lg z-50">
              <div className="container mx-auto max-w-md">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center gap-2">
                    <ShoppingCart className="h-4 w-4 text-primary" />
                    <span className="font-medium">
                      {getTotalTickets()} ticket{getTotalTickets() !== 1 ? 's' : ''} selected
                    </span>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-primary">RM {calculateTotal().toFixed(2)}</div>
                    <div className="text-xs text-muted-foreground">Total</div>
                  </div>
                </div>
                <Button
                  onClick={handleContinue}
                  className="w-full"
                  size="lg"
                  disabled={isProcessing}
                >
                  {isProcessing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                      Processing...
                    </>
                  ) : (
                    <>
                      {currentStep === "selection" ? "Review Order" : "Continue to Registration"}
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </div>
            </div>
          )}

          {/* Mobile Spacer */}
          <div className="lg:hidden h-32"></div>
        </div>
      </main>
    </div>
  )
}