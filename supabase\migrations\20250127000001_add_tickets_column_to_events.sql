-- Add tickets column to events table for ticket configuration
-- This allows events to have multiple ticket types with different pricing and features

DO $$ 
BEGIN
  -- Add tickets column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'events' AND column_name = 'tickets') THEN
    ALTER TABLE events ADD COLUMN tickets JSONB DEFAULT '[]';
  END IF;
END $$;

-- Create index for better performance on tickets column
CREATE INDEX IF NOT EXISTS idx_events_tickets ON events USING GIN(tickets);

-- Add comment to document the tickets column
COMMENT ON COLUMN events.tickets IS 'Array of ticket type configurations with pricing and features';

-- Example ticket structure:
-- [
--   {
--     "ticketType": {
--       "id": "uuid",
--       "name": "Early Bird",
--       "description": "Early bird special pricing",
--       "price": 50.00,
--       "maxQuantity": 5,
--       "availableQuantity": 100,
--       "features": ["Event access", "Welcome kit", "Lunch included"],
--       "isPopular": false,
--       "saleStartDate": "2024-01-01T00:00:00Z",
--       "saleEndDate": "2024-02-01T00:00:00Z"
--     },
--     "quantity": 1
--   }
-- ]
